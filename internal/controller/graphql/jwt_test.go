package graphql

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

func TestGqlJwtAuth_ValidToken(t *testing.T) {
	// Setup test config
	global.GVA_CONFIG = config.Server{
		JWT: config.JWT{
			SigningKey:  "test-secret-key",
			ExpiresTime: "24h",
			BufferTime:  "1h",
			Issuer:      "xbit-agent-test",
		},
	}

	// Create test user data
	userID := uuid.New()
	email := "<EMAIL>"

	// Generate valid JWT token
	token, err := utils.GenerateJWTToken(userID, email, global.GVA_CONFIG.JWT)
	if err != nil {
		t.Fatalf("Failed to generate JWT token: %v", err)
	}

	// Setup Gin router with middleware
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(GqlJwtAuth())

	// Test endpoint that checks context
	router.GET("/test", func(c *gin.Context) {
		userId := GetUserIDFromContext(c.Request.Context())
		userEmail := GetUserEmailFromContext(c.Request.Context())

		if userId == uuid.Nil {
			c.JSON(400, gin.H{"error": "No user ID in context"})
			return
		}

		if userEmail == "" {
			c.JSON(400, gin.H{"error": "No user email in context"})
			return
		}

		c.JSON(200, gin.H{
			"userId":    userId.String(),
			"userEmail": userEmail,
		})
	})

	// Create test request with valid JWT token
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("X-Consumer-Username", "xbit")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}
}

func TestGqlJwtAuth_InvalidToken(t *testing.T) {
	// Setup test config
	global.GVA_CONFIG = config.Server{
		JWT: config.JWT{
			SigningKey:  "test-secret-key",
			ExpiresTime: "24h",
			BufferTime:  "1h",
			Issuer:      "xbit-agent-test",
		},
	}

	// Setup Gin router with middleware
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(GqlJwtAuth())

	// Test endpoint that checks context
	router.GET("/test", func(c *gin.Context) {
		userId := GetUserIDFromContext(c.Request.Context())
		if userId == uuid.Nil {
			c.JSON(200, gin.H{"message": "No user context (expected for invalid token)"})
			return
		}
		c.JSON(400, gin.H{"error": "Unexpected user context"})
	})

	// Create test request with invalid JWT token
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Bearer invalid-token")
	req.Header.Set("X-Consumer-Username", "xbit")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}
}

func TestGqlJwtAuth_NoToken(t *testing.T) {
	// Setup Gin router with middleware
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(GqlJwtAuth())

	// Test endpoint that checks context
	router.GET("/test", func(c *gin.Context) {
		userId := GetUserIDFromContext(c.Request.Context())
		if userId == uuid.Nil {
			c.JSON(200, gin.H{"message": "No user context (expected for no token)"})
			return
		}
		c.JSON(400, gin.H{"error": "Unexpected user context"})
	})

	// Create test request without JWT token
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("X-Consumer-Username", "xbit")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d. Response: %s", w.Code, w.Body.String())
	}
}

func TestAuthDirective_ValidUser(t *testing.T) {
	userID := uuid.New()
	ctx := context.WithValue(context.Background(), "userId", userID.String())

	// Mock resolver that just returns success
	mockResolver := func(ctx context.Context) (interface{}, error) {
		return "success", nil
	}

	result, err := AuthDirective(ctx, nil, mockResolver)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if result != "success" {
		t.Errorf("Expected 'success', got %v", result)
	}
}

func TestAuthDirective_NoUser(t *testing.T) {
	ctx := context.Background()

	// Mock resolver that should not be called
	mockResolver := func(ctx context.Context) (interface{}, error) {
		t.Error("Resolver should not be called when user is not authenticated")
		return nil, nil
	}

	_, err := AuthDirective(ctx, nil, mockResolver)
	if err == nil {
		t.Error("Expected error for unauthenticated user, got nil")
	}

	if err != utils.ErrAccessTokenInvalid {
		t.Errorf("Expected ErrAccessTokenInvalid, got %v", err)
	}
}
