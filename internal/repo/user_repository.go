package repo

import (
	"context"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

type UserRepositoryInterface interface {
	Create(ctx context.Context, user *model.User) error
	CreateWallet(ctx context.Context, wallet *model.UserWallet) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.User, error)
	GetByEmail(ctx context.Context, email string) (*model.User, error)
	GetByInvitationCode(ctx context.Context, code string) (*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error)
	CreateUserWallet(ctx context.Context, wallet *model.UserWallet) error
	GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error)
	GetDirectReferral(ctx context.Context, userID, referrerID uuid.UUID) (*model.Referral, error)
	GetAllReferrals(ctx context.Context, tx *gorm.DB, userID uuid.UUID) ([]model.Referral, error)
	CreateReferral(ctx context.Context, referral *model.Referral) error
	GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
	UpdateReferralSnapshot(ctx context.Context, snapshot *model.ReferralSnapshot) error
	GetReferrerByUserID(ctx context.Context, referrerID uuid.UUID) (model.Referral, error)
	GetReferrerByReferrerID(ctx context.Context, referrerID uuid.UUID) (model.Referral, error)
	HasDirectReferral(ctx context.Context, userID uuid.UUID) (bool, error)
	WithTransaction(ctx context.Context, fn func(ctx context.Context) (*model.User, error)) (*model.User, error)
	IsInUpline(ctx context.Context, referrerID, userID uuid.UUID) (bool, error)
}

type UserRepository struct {
	db *gorm.DB
}

func NewUserRepository() UserRepositoryInterface {
	return &UserRepository{
		db: global.GVA_DB,
	}
}

func (r *UserRepository) Create(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Create(user).Error
}
func (r *UserRepository) CreateWallet(ctx context.Context, wallet *model.UserWallet) error {
	return r.db.WithContext(ctx).Create(wallet).Error
}

func (r *UserRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	var user model.User
	err := r.db.Debug().WithContext(ctx).
		First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).
		Preload("Wallets").
		Preload("Referrals").
		Preload("Referrals.Referrer").
		Preload("ReferralSnapshot").
		Preload("ReferredUsers").
		First(&user, "email = ?", email).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetByInvitationCode(ctx context.Context, code string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).First(&user, "invitation_code = ?", code).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) Update(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Save(user).Error
}

func (r *UserRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.User{}, "id = ?", id).Error
}

func (r *UserRepository) GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error) {
	var wallets []model.UserWallet
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&wallets).Error
	return wallets, err
}

func (r *UserRepository) CreateUserWallet(ctx context.Context, wallet *model.UserWallet) error {
	return r.db.WithContext(ctx).Create(wallet).Error
}

func (r *UserRepository) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		Preload("Referrer.Referral").
		First(&referral, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

func (r *UserRepository) GetDirectReferral(ctx context.Context, userID, referrerID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		First(&referral, "user_id = ? AND referrer_id = ?", userID, referrerID).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

func (r *UserRepository) IsInUpline(ctx context.Context, referrerID, userID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.Referral{}).
		Where("user_id = ? AND referrer_id = ?", referrerID, userID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// Check if a user already has any direct referrals (Depth = 1)
func (r *UserRepository) HasDirectReferral(ctx context.Context, userID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Referral{}).
		Where("user_id = ? AND depth = 1", userID).
		Count(&count).Error

	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *UserRepository) GetAllReferrals(ctx context.Context, tx *gorm.DB, userID uuid.UUID) ([]model.Referral, error) {
	var referrals []model.Referral
	err := tx.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		Where("user_id = ?", userID).
		Find(&referrals).Error
	if err != nil {
		return nil, err
	}
	return referrals, nil
}

func (r *UserRepository) GetReferrerByUserID(ctx context.Context, userID uuid.UUID) (model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&referral, "referrer_id = ?", userID).Error
	if err != nil {
		return model.Referral{}, err
	}
	return referral, nil
}

func (r *UserRepository) GetReferrerByReferrerID(ctx context.Context, referrerID uuid.UUID) (model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&referral, "user_id = ?", referrerID).Error
	if err != nil {
		return model.Referral{}, err
	}
	return referral, nil
}

func (r *UserRepository) CreateReferral(ctx context.Context, referral *model.Referral) error {
	return r.db.WithContext(ctx).Create(referral).Error
}

func (r *UserRepository) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	var snapshot model.ReferralSnapshot
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&snapshot, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &snapshot, nil
}

func (r *UserRepository) UpdateReferralSnapshot(ctx context.Context, snapshot *model.ReferralSnapshot) error {
	return r.db.WithContext(ctx).Save(snapshot).Error
}

// WithTransaction executes a function within a database transaction
func (r *UserRepository) WithTransaction(ctx context.Context, fn func(ctx context.Context) (*model.User, error)) (*model.User, error) {
	var result *model.User
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Replace the current repository with transaction repository temporarily
		originalDB := r.db
		r.db = tx
		defer func() {
			r.db = originalDB
		}()

		var err error
		result, err = fn(ctx)
		return err
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}
