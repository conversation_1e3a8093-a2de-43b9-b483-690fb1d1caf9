package task

import (
	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

type UserEvent string

const (
	UserNewWalletSubject UserEvent = "dex.user.wallet.new"
)

type UserWalletInfo struct {
	ID              string          `json:"id"`
	Chain           model.ChainType `json:"chain"`
	WalletAddress   string          `json:"walletAddress"`
	WalletAccountID uuid.UUID       `json:"walletAccountId"`
	WalletID        uuid.UUID       `json:"walletId"`
	CreatedAt       string          `json:"createdAt"`
}

type UserNewWalletEvent struct {
	UserID  uuid.UUID        `json:"userId"`
	Wallets []UserWalletInfo `json:"wallets"`
}
