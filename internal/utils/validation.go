package utils

import (
	"regexp"
	"strings"
)

// IsValidEmail validates email format using regex
func IsValidEmail(email string) bool {
	if email == "" {
		return true // Empty email is considered valid (will be stored as empty string)
	}

	// Basic email regex pattern
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	re := regexp.MustCompile(emailRegex)
	return re.MatchString(strings.TrimSpace(email))
}

// NormalizeEmail normalizes email by trimming whitespace and converting to lowercase
func NormalizeEmail(email string) string {
	if email == "" {
		return ""
	}
	return strings.ToLower(strings.TrimSpace(email))
}

// StringToPointer converts string to *string, returns nil for empty strings
func StringToPointer(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// PointerToString converts *string to string, returns empty string for nil
func PointerToString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}
