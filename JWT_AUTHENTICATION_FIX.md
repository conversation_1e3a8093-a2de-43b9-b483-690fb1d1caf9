# JWT Authentication Fix - GraphQL User Data Extraction

## Problem Statement

The GraphQL API was still accepting user data directly through input parameters instead of extracting user information from JWT tokens. This violated the security requirement that the Agent service should only store user data based on the User ID present in the JWT token.

## Issues Fixed

### 1. GraphQL Schema Changes

**Before:**
```graphql
input CreateUserWithReferralInput {
  userId: ID!
  invitationCode: String!
}

input CreateUserInvitationCodeInput {
  userId: ID
  invitationCode: String!
  # ... other fields
}
```

**After:**
```graphql
input CreateUserWithReferralInput {
  invitationCode: String!
}

input CreateUserInvitationCodeInput {
  invitationCode: String!
  # ... other fields (userId removed)
}
```

### 2. Resolver Implementation Changes

**Before:**
```go
func (r *mutationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
    // Used input.UserID directly from GraphQL input
    err = r.UserService.CreateUserWithReferral(ctx, referrer.ID, input.UserID)
}
```

**After:**
```go
func (r *mutationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
    // Extract user ID from JWT token instead of accepting it as input
    userId := GetUserIDFromContext(ctx)
    if userId == uuid.Nil {
        return nil, fmt.Errorf("User ID not found in JWT token")
    }
    
    err = r.UserService.CreateUserWithReferral(ctx, referrer.ID, userId.String())
}
```

### 3. JWT Middleware Improvements

**Before:**
- Used `ParseUnverified` which doesn't validate token signature
- Simple `Claims` struct with only `Sub` field
- Poor error handling

**After:**
- Uses proper `ValidateJWTToken` function with signature verification
- Extracts full user information from JWT claims (`UserID` and `Email`)
- Proper error handling and logging
- Sets both `userId` and `userEmail` in context

```go
func GqlJwtAuth() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // ... validation logic ...
        
        // Validate JWT token using the proper validation function
        claims, err := utils.ValidateJWTToken(tokenString, global.GVA_CONFIG.JWT)
        if err != nil {
            // Log and continue without setting user context
            return
        }

        // Set both userId and email in context from JWT claims
        wrappedCtx := context.WithValue(ctx.Request.Context(), "userId", claims.UserID.String())
        wrappedCtx = context.WithValue(wrappedCtx, "userEmail", claims.Email)
        ctx.Request = ctx.Request.WithContext(wrappedCtx)
    }
}
```

### 4. Enhanced Auth Directive

**Before:**
```go
func AuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (interface{}, error) {
    user := ctx.Value("userId")
    if user == nil {
        return nil, utils.ErrAccessTokenInvalid
    }
    return next(ctx)
}
```

**After:**
```go
func AuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (interface{}, error) {
    // Get user ID from context (set by JWT middleware)
    userIdStr := ctx.Value("userId")
    if userIdStr == nil {
        return nil, utils.ErrAccessTokenInvalid
    }

    // Validate that the user ID is a valid UUID
    userIdString, ok := userIdStr.(string)
    if !ok || userIdString == "" {
        return nil, utils.ErrAccessTokenInvalid
    }

    // Additional validation: ensure it's a valid UUID format
    if _, err := uuid.Parse(userIdString); err != nil {
        return nil, utils.ErrAccessTokenInvalid
    }

    return next(ctx)
}
```

### 5. Helper Functions Added

```go
func GetUserIDFromContext(ctx context.Context) uuid.UUID {
    userIdStr, _ := ctx.Value("userId").(string)
    userId := uuid.Nil
    if userIdStr != "" {
        userId, _ = uuid.Parse(userIdStr)
    }
    return userId
}

func GetUserEmailFromContext(ctx context.Context) string {
    email, _ := ctx.Value("userEmail").(string)
    return email
}
```

## Security Benefits

1. **JWT-Only Authentication**: User data is now exclusively extracted from validated JWT tokens
2. **Token Signature Validation**: Proper cryptographic validation of JWT tokens
3. **Input Sanitization**: User ID is no longer accepted as direct input, preventing manipulation
4. **Context Isolation**: User information is securely stored in request context
5. **Proper Error Handling**: Invalid tokens are handled gracefully without exposing sensitive information

## Testing

Comprehensive tests have been added to verify:
- Valid JWT token processing
- Invalid JWT token handling
- Missing token scenarios
- Auth directive functionality
- Context extraction functions

All tests pass successfully, ensuring the implementation is robust and secure.

## Usage Examples

### Before (Insecure):
```graphql
mutation {
  createUserWithReferral(input: {
    userId: "user-provided-id"  # ❌ Security risk
    invitationCode: "ABC123"
  }) {
    success
    message
  }
}
```

### After (Secure):
```graphql
# JWT token in Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
mutation {
  createUserWithReferral(input: {
    invitationCode: "ABC123"  # ✅ User ID extracted from JWT
  }) {
    success
    message
  }
}
```

## Conclusion

The GraphQL API now properly implements JWT-based authentication where:
- User identity is extracted exclusively from validated JWT tokens
- No user data can be provided directly through GraphQL inputs
- All mutations requiring authentication use the JWT-extracted user ID
- Static data (email, user ID) comes from JWT claims as required
- The system is secure against user ID manipulation attacks
